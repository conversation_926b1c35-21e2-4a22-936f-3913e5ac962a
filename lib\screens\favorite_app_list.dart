// ignore_for_file: must_be_immutable
import 'dart:io';
import 'package:a20sph/widgets/cached_app_icon.dart';
import 'package:a20sph/widgets/digital_clock.dart';
import 'package:a20sph/screens/start_blocked_app.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:installed_apps/installed_apps.dart';

import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:a20sph/services/admob_service.dart';
import 'package:device_apps/device_apps.dart';

class FavoriteAppsList extends StatefulWidget {
  const FavoriteAppsList({
    super.key,
    required this.showStartUpMessage,
    required this.showTime,
    required this.startUpMessage,
    required this.bgFilePath,
    required this.blockedAppTime,
    required this.numQuestion,
    required this.quizData,
    required this.quizLevel
  });

  // Settings attributes
  final String startUpMessage;
  final bool showTime;
  final bool showStartUpMessage;
  final String bgFilePath;
  final int blockedAppTime;
  final int numQuestion;
  final int quizLevel;
  final dynamic quizData;

  @override State<FavoriteAppsList> createState() => _FavoriteAppsListState();
}

class _FavoriteAppsListState extends State<FavoriteAppsList> {
  BannerAd? _banner;
  List<Application> _apps = [];
  bool _isLoading = true;
  List<String> _lastFavoritesList = [];

  void _createBannerAd() {
    _banner = BannerAd(
      size: AdSize.banner,
      adUnitId: AdMobService.bannerAdId!,
      listener: AdMobService.bannerListener,
      request: const AdRequest(),
    )..load();
  }

  Future<void> _loadApps() async {
    try {
      List<dynamic> allowedPackageNames = Hive.box("apps").get("favorite") ?? [];
      if (allowedPackageNames.isEmpty) {
        if (mounted) {
          setState(() {
            _apps = [];
            _isLoading = false;
          });
        }
        return;
      }

      List<Application> allApps = await DeviceApps.getInstalledApplications(
        includeAppIcons: true,
        includeSystemApps: true
      );

      List<Application> filteredApps = allApps
          .where((app) => allowedPackageNames.contains("${app.packageName}-${app.appName}"))
          .toList();

      if (mounted) {
        setState(() {
          _apps = filteredApps;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _apps = [];
          _isLoading = false;
        });
      }
    }
  }

  // Method to load apps from favorites list
  Future<void> _loadAppsFromFavorites(List<dynamic> allowedPackageNames) async {
    if (!mounted) return;

    try {
      if (allowedPackageNames.isEmpty) {
        setState(() {
          _apps = [];
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _isLoading = true;
      });

      List<Application> allApps = await DeviceApps.getInstalledApplications(
        includeAppIcons: true,
        includeSystemApps: true
      );

      List<Application> filteredApps = allApps
          .where((app) => allowedPackageNames.contains("${app.packageName}-${app.appName}"))
          .toList();

      if (mounted) {
        setState(() {
          _apps = filteredApps;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _apps = [];
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to compare two lists
  bool _listEquals(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  @override
  void initState() {
    super.initState();
    // Initialize the last favorites list
    List<dynamic> currentFavorites = Hive.box("apps").get("favorite") ?? [];
    _lastFavoritesList = currentFavorites.map((e) => e.toString()).toList();
    _loadApps();
    _createBannerAd();
  }

  @override
  void dispose() {
    _banner?.dispose();
    super.dispose();
  }

  // Method to refresh apps when favorites change
  void refreshApps() {
    _loadApps();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Background image
            Positioned.fill(
              child: widget.bgFilePath.isEmpty
                ? const Image(
                    image: AssetImage("lib/assets/images/bg.jfif"),
                    fit: BoxFit.cover,
                  )
                : Image.file(
                    File(widget.bgFilePath),
                    fit: BoxFit.cover,
                  ),
            ),

            // Main content - Listen to Hive changes
            ValueListenableBuilder(
              valueListenable: Hive.box("apps").listenable(),
              builder: (context, box, child) {
                List<dynamic> currentFavorites = box.get("favorite") ?? [];
                List<String> currentFavoritesStr = currentFavorites.map((e) => e.toString()).toList();

                // Check if favorites list has changed
                if (!_listEquals(_lastFavoritesList, currentFavoritesStr)) {
                  _lastFavoritesList = List.from(currentFavoritesStr);
                  // Schedule app reload for next frame to avoid setState during build
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    debugPrint('Favorites changed, reloading apps: ${currentFavorites.length} favorites');
                    _loadAppsFromFavorites(currentFavorites);
                  });
                }

                return Column(
                  children: [
                    const SizedBox(height: 20),

                    // Clock widget (only show if enabled)
                    if (widget.showTime)
                      const DigitalClock(),

                    const SizedBox(height: 20),

                    // App grid
                    Expanded(
                      child: _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _buildAppGrid(),
                    ),

                    // Banner ad
                    if (_banner != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        height: 52,
                        child: AdWidget(ad: _banner!),
                      )
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppGrid() {
    if (_apps.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64,
              color: Colors.white54,
            ),
            SizedBox(height: 16),
            Text(
              'No favorite apps yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white54,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Add apps to favorites from the app list',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white38,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _apps.length,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemBuilder: (context, index) {
        final app = _apps[index];
        return CachedAppIcon(
          app: app,
          onTap: () => _handleAppTap(app, index),
        );
      },
    );
  }

  Future<void> _handleAppTap(Application app, int index) async {
    try {
      List<dynamic> favoriteApps = Hive.box("apps").get("favorite") ?? [];
      if (index >= favoriteApps.length) return;

      List<String> appData = favoriteApps[index].toString().split("-");
      String pkgName = appData[0];
      String appIdentifier = appData.join("-");

      List<dynamic> blockedApps = Hive.box("apps").get("blocked") ?? [];

      if (blockedApps.contains(appIdentifier)) {
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StartBlockedApp(
                appName: appIdentifier,
                timeBtwQuiz: widget.blockedAppTime,
                numQuestion: widget.numQuestion,
                quizData: widget.quizData,
                quizLevel: widget.quizLevel,
              ),
            ),
          );
        }
      } else {
        await InstalledApps.startApp(pkgName);
      }
    } catch (e) {
      // Handle error silently or show a snackbar if needed
      debugPrint('Error launching app: $e');
    }
  }
}