import 'package:a20sph/services/admob_service.dart';
import 'package:a20sph/widgets/quiz_widget.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class StartBlockedApp extends StatefulWidget {
  final String appName;
  final int timeBtwQuiz;
  final int numQuestion;
  final int quizLevel;
  final dynamic quizData;

  const StartBlockedApp({super.key, required this.appName, required this.timeBtwQuiz, required this.numQuestion, required this.quizData, required this.quizLevel});
  // widget.appName.split("-")[0]

  @override
  State<StartBlockedApp> createState() => _StartBlockedAppState();
}

class _StartBlockedAppState extends State<StartBlockedApp> {
  BannerAd? _banner;

  @override
  void initState() {
    super.initState();
    _createBannerAd();
  }

  void _createBannerAd() {
    _banner = BannerAd(
      size: AdSize.banner,
      adUnitId: AdMobService.bannerAdId!,
      listener: AdMobService.bannerListener,
      request: const AdRequest(),
    )..load();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: QuizWidget(appName: widget.appName, timeBtwQuiz: widget.timeBtwQuiz, numQuestion: widget.numQuestion, quizData: widget.quizData, quizLevel: widget.quizLevel)
      ),
      bottomNavigationBar: _banner == null
      ? Container()
      : Container(
        margin: const EdgeInsets.only(bottom: 12),
        height: 52,
        child: AdWidget(ad: _banner!),
      )
    );
  }
}